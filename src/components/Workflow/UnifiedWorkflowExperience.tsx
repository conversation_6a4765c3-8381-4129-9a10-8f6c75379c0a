/**
 * Unified Workflow Experience
 * 
 * Single-page workflow experience with integrated agent collaboration
 * and real-time backend integration
 */

'use client';

import { useState, useEffect, useCallback } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import ExecutionDashboard from './ExecutionDashboard';
import HumanReviewInterface from './HumanReviewInterface';
import ResultsDashboard from './ResultsDashboard';
import ErrorBoundary from './ErrorBoundary';
import WorkflowNavigationSystem, { useWorkflowNavigation } from './WorkflowNavigationSystem';

// Types
interface WorkflowTemplate {
  id: string;
  name: string;
  description: string;
  category: string;
  tags: string[];
  difficulty: string;
  estimatedTime: number;
  consultationEnabled: boolean;
  agentCount: number;
  steps: Array<{
    id: string;
    name: string;
    type: string;
    dependencies?: string[];
  }>;
}

interface WorkflowExecution {
  id: string;
  status: 'running' | 'paused' | 'completed' | 'failed' | 'waiting_review';
  progress: number;
  currentStep?: string;
  steps: Array<{
    id: string;
    name: string;
    status: string;
    outputs?: any;
    artifactId?: string;
  }>;
}

interface WorkflowResults {
  execution: {
    id: string;
    workflowId: string;
    status: string;
    progress: number;
    startedAt: string;
    completedAt?: string;
    inputs: Record<string, any>;
    currentStep?: string;
    metadata?: Record<string, any>;
  };
  workflow: {
    id: string;
    name: string;
    description: string;
  } | null;
  steps: Array<{
    stepId: string;
    status: string;
    startedAt: string;
    completedAt?: string;
    duration?: number;
    inputs: Record<string, any>;
    outputs: Record<string, any>;
    error?: string;
    stepType?: string;
    approvalRequired?: boolean;
    artifactId?: string;
  }>;
  content: Array<{
    id: string;
    type: string;
    title: string;
    content: any;
    status: string;
    stepId: string;
    createdAt: string;
    metadata?: Record<string, any>;
  }>;
  artifacts: Array<{
    id: string;
    type: string;
    title: string;
    content: any;
    status: string;
    stepId: string;
    executionId: string;
    createdAt: string;
    approvedBy?: string;
    approvedAt?: string;
    metadata?: Record<string, any>;
  }>;
}

interface AgentActivity {
  agentId: string;
  status: 'idle' | 'analyzing' | 'responding' | 'waiting' | 'completed';
  lastSeen: string;
}

type WorkflowStep = 'template' | 'configure' | 'executing' | 'collaboration' | 'review' | 'results';

interface Props {
  onComplete?: (executionId: string) => void;
  onBack?: () => void;
  initialStep?: WorkflowStep;
  initialExecutionId?: string;
  initialReviewId?: string;
}

export default function UnifiedWorkflowExperience({
  onComplete,
  onBack,
  initialStep = 'template',
  initialExecutionId,
  initialReviewId
}: Props) {
  const router = useRouter();
  const searchParams = useSearchParams();

  // Core state
  const [currentStep, setCurrentStep] = useState<WorkflowStep>(initialStep);
  const [selectedTemplate, setSelectedTemplate] = useState<WorkflowTemplate | null>(null);
  const [workflowInputs, setWorkflowInputs] = useState<Record<string, any>>({});
  const [execution, setExecution] = useState<WorkflowExecution | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Results state
  const [results, setResults] = useState<WorkflowResults | null>(null);
  const [publishingStatus, setPublishingStatus] = useState<Record<string, 'idle' | 'publishing' | 'published' | 'failed'>>({});
  const [publishResults, setPublishResults] = useState<Record<string, { url?: string; error?: string }>>({});

  // Review state
  const [reviewData, setReviewData] = useState<any>(null);
  const [reviewVersions, setReviewVersions] = useState<any[]>([]);
  const [showVersionComparison, setShowVersionComparison] = useState(false);

  // Navigation state
  const navigationState = useWorkflowNavigation(currentStep, execution?.id || initialExecutionId);

  // Agent collaboration state
  const [, setAgentActivities] = useState<AgentActivity[]>([]);
  const [isCollaborationActive, setIsCollaborationActive] = useState(false);
  const [, setCollaborationResult] = useState<any>(null);

  // UI state
  const [notifications, setNotifications] = useState<string[]>([]);
  const [templates, setTemplates] = useState<WorkflowTemplate[]>([]);

  // Initialize from URL parameters (only on mount)
  useEffect(() => {
    const step = searchParams.get('step') as WorkflowStep;
    const executionId = searchParams.get('executionId') || initialExecutionId;
    const reviewId = searchParams.get('reviewId') || initialReviewId;

    console.log('🔍 URL Parameters on mount:', { step, executionId, reviewId, currentStep });

    // Only set step from URL on initial load, not on every URL change
    if (step && step !== 'null' && currentStep === initialStep) {
      console.log('🔄 Setting initial step from URL to:', step);
      setCurrentStep(step);
    } else if (!step || (step as string) === 'null') {
      // If no step is specified or step is null, default to template
      if (currentStep === initialStep) {
        console.log('🔄 Setting default step to template');
        setCurrentStep('template');
      }
    }

    if (executionId && (step === 'results' || initialStep === 'results')) {
      loadResults(executionId);
    }

    if (reviewId && (step === 'review' || initialStep === 'review')) {
      loadReviewData(reviewId);
    }
  }, []); // Only run on mount

  // Load templates on mount
  useEffect(() => {
    console.log('🔄 Loading templates on mount...');
    loadTemplates();
  }, []);

  // Auto-refresh execution status
  useEffect(() => {
    if (execution?.id && (currentStep === 'executing' || currentStep === 'collaboration')) {
      const interval = setInterval(() => {
        refreshExecutionStatus();
      }, 2000);
      return () => clearInterval(interval);
    }
  }, [execution?.id, currentStep]);

  // Update URL when step changes
  useEffect(() => {
    const params = new URLSearchParams();
    params.set('step', currentStep);

    if (execution?.id) {
      params.set('executionId', execution.id);
    }

    if (reviewData?.id) {
      params.set('reviewId', reviewData.id);
    }

    const newUrl = `/workflow/unified?${params.toString()}`;
    console.log('🔗 Updating URL to:', newUrl);
    router.replace(newUrl, { scroll: false });
  }, [currentStep, execution?.id, reviewData?.id, router]);

  const addNotification = useCallback((message: string) => {
    setNotifications(prev => [...prev.slice(-2), message]);
    setTimeout(() => {
      setNotifications(prev => prev.slice(1));
    }, 5000);
  }, []);

  const loadResults = async (executionId: string) => {
    if (!executionId) {
      setError('No execution ID provided');
      return;
    }

    try {
      setIsLoading(true);
      const response = await fetch(`/api/workflow/results/${executionId}`);

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const result = await response.json();

      if (result.success && result.data) {
        const execution = result.data.execution;
        const workflow = result.data.workflow;
        const artifacts = result.data.artifacts || [];
        const steps = result.data.steps || [];

        const transformedResults: WorkflowResults = {
          execution: {
            id: execution?.id || executionId,
            workflowId: execution?.workflowId || 'unknown',
            status: execution?.status || 'unknown',
            progress: execution?.progress || 0,
            startedAt: execution?.startedAt || new Date().toISOString(),
            completedAt: execution?.completedAt,
            inputs: execution?.inputs || {},
            currentStep: execution?.currentStep
          },
          workflow: workflow ? {
            id: workflow.id || 'unknown',
            name: workflow.name || 'Unknown Workflow',
            description: workflow.description || 'No description available'
          } : null,
          steps: steps.length > 0 ? steps : Object.values(execution?.stepResults || {}).map((step: any) => ({
            stepId: step?.stepId || 'unknown',
            status: step?.status || 'unknown',
            startedAt: step?.startedAt || new Date().toISOString(),
            completedAt: step?.completedAt,
            duration: step?.duration,
            inputs: step?.inputs || {},
            outputs: step?.outputs || {},
            error: step?.error,
            stepType: step?.stepType,
            approvalRequired: step?.approvalRequired,
            artifactId: step?.artifactId
          })),
          content: [],
          artifacts: artifacts.map((artifact: any) => ({
            ...artifact,
            id: artifact.id || `artifact-${Date.now()}`,
            title: artifact.title || 'Untitled Artifact',
            content: artifact.content || '',
            status: artifact.status || 'draft',
            stepId: artifact.stepId || 'unknown',
            executionId: artifact.executionId || executionId,
            createdAt: artifact.createdAt || new Date().toISOString()
          }))
        };

        setResults(transformedResults);
        setCurrentStep('results');
        addNotification('✅ Results loaded successfully');
      } else {
        setError(result.error || 'Failed to load results');
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to load results';
      setError(errorMessage);
      console.error('Load results error:', err);
    } finally {
      setIsLoading(false);
    }
  };

  const loadReviewData = async (reviewId: string) => {
    try {
      setIsLoading(true);
      const response = await fetch(`/api/review/${reviewId}`);
      const result = await response.json();

      if (result.success) {
        setReviewData(result.data);
        setCurrentStep('review');
        addNotification('✅ Review data loaded');

        // Load versions
        const versionsResponse = await fetch(`/api/review/${reviewId}/versions`);
        const versionsResult = await versionsResponse.json();
        if (versionsResult.success) {
          setReviewVersions(versionsResult.data.versions || []);
        }
      } else {
        setError(result.error || 'Failed to load review');
      }
    } catch (err) {
      setError('Failed to load review');
      console.error(err);
    } finally {
      setIsLoading(false);
    }
  };

  const loadTemplates = async () => {
    try {
      setIsLoading(true);
      const response = await fetch('/api/workflow/templates');
      const result = await response.json();

      console.log('📋 Templates API response:', result);

      if (result.success) {
        // Handle both possible response formats
        const templatesData = result.data.templates || result.data;
        console.log('📋 Templates data:', templatesData);

        if (templatesData && Array.isArray(templatesData) && templatesData.length > 0) {
          console.log('📋 First template structure:', templatesData[0]);
        }

        setTemplates(templatesData || []);
        console.log('📋 Templates set to state:', templatesData);
        addNotification(`✅ Loaded ${(templatesData || []).length} templates`);
      } else {
        // Fallback to enhanced template selector data
        const fallbackTemplates: WorkflowTemplate[] = [
          {
            id: 'blog-post-seo',
            name: 'SEO Blog Post',
            description: 'Complete SEO-optimized blog post generation with keyword research and agent consultation',
            category: 'blog',
            tags: ['seo', 'blog', 'content-marketing'],
            difficulty: 'easy',
            estimatedTime: 45,
            consultationEnabled: true,
            agentCount: 3,
            steps: [
              { id: 'topic-input', name: 'Topic Input', type: 'TEXT_INPUT' },
              { id: 'keyword-research', name: 'Keyword Research', type: 'AI_GENERATION' },
              { id: 'content-creation', name: 'Content Creation', type: 'AI_GENERATION' },
              { id: 'human-review', name: 'Human Review', type: 'HUMAN_REVIEW' },
              { id: 'seo-optimization', name: 'SEO Optimization', type: 'AI_GENERATION' }
            ]
          },
          {
            id: 'product-descriptions',
            name: 'Bulk Product Descriptions',
            description: 'Generate product descriptions in bulk with brand voice consistency',
            category: 'ecommerce',
            tags: ['ecommerce', 'bulk', 'product-descriptions'],
            difficulty: 'medium',
            estimatedTime: 120,
            consultationEnabled: true,
            agentCount: 2,
            steps: [
              { id: 'csv-import', name: 'Import Product Data', type: 'CSV_IMPORT' },
              { id: 'brand-voice-input', name: 'Brand Voice Guidelines', type: 'TEXT_INPUT' },
              { id: 'bulk-generation', name: 'Generate Descriptions', type: 'LOOP' }
            ]
          }
        ];
        setTemplates(fallbackTemplates);
        addNotification(`⚠️ Using fallback templates (${fallbackTemplates.length})`);
      }
    } catch (error) {
      console.error('❌ Failed to load templates:', error);
      addNotification('❌ Failed to load templates, using fallback');

      // Use fallback templates on error
      const fallbackTemplates: WorkflowTemplate[] = [
        {
          id: 'blog-post-seo',
          name: 'SEO Blog Post',
          description: 'Complete SEO-optimized blog post generation with keyword research and agent consultation',
          category: 'blog',
          tags: ['seo', 'blog', 'content-marketing'],
          difficulty: 'easy',
          estimatedTime: 45,
          consultationEnabled: true,
          agentCount: 3,
          steps: [
            { id: 'topic-input', name: 'Topic Input', type: 'TEXT_INPUT' },
            { id: 'keyword-research', name: 'Keyword Research', type: 'AI_GENERATION' },
            { id: 'content-creation', name: 'Content Creation', type: 'AI_GENERATION' },
            { id: 'human-review', name: 'Human Review', type: 'HUMAN_REVIEW' },
            { id: 'seo-optimization', name: 'SEO Optimization', type: 'AI_GENERATION' }
          ]
        }
      ];
      setTemplates(fallbackTemplates);
    } finally {
      setIsLoading(false);
    }
  };

  const refreshExecutionStatus = async () => {
    if (!execution?.id) return;

    try {
      const response = await fetch(`/api/workflow/execution/${execution.id}`);
      const result = await response.json();
      
      if (result.success) {
        const updatedExecution = result.data;

        setExecution(prev => prev ? {
          ...prev,
          status: updatedExecution.status || prev.status,
          progress: updatedExecution.progress !== undefined ? updatedExecution.progress : prev.progress,
          currentStep: updatedExecution.currentStep || prev.currentStep,
          steps: updatedExecution.steps ? updatedExecution.steps.map((updatedStep: any) => ({
            id: updatedStep.id,
            name: updatedStep.name,
            status: updatedStep.status || 'pending',
            outputs: updatedStep.outputs
          })) : prev.steps
        } : null);

        // Handle status changes
        if (updatedExecution.status === 'waiting_review' && currentStep !== 'review') {
          setCurrentStep('review');
          addNotification('Workflow paused for human review');
        } else if (updatedExecution.status === 'completed' && currentStep !== 'results') {
          setCurrentStep('results');
          addNotification('Workflow completed successfully!');
          if (onComplete) {
            onComplete(execution.id);
          }
        } else if (updatedExecution.status === 'failed') {
          setError('Workflow execution failed');
          addNotification('Workflow execution failed');
        }

        // Update agent activities based on execution status
        if (updatedExecution.status === 'running') {
          setAgentActivities([
            { agentId: 'seo-keyword', status: 'analyzing', lastSeen: new Date().toISOString() },
            { agentId: 'market-research', status: 'analyzing', lastSeen: new Date().toISOString() },
            { agentId: 'content-strategy', status: 'analyzing', lastSeen: new Date().toISOString() }
          ]);
        }
      }
    } catch (error) {
      console.error('Failed to refresh execution status:', error);
    }
  };

  const startStatusPolling = (executionId: string) => {
    console.log('📊 Starting status polling for execution:', executionId);

    const pollInterval = setInterval(async () => {
      try {
        const response = await fetch(`/api/workflow/execution/${executionId}`);
        const result = await response.json();

        if (result.success) {
          const updatedExecution = result.data;
          console.log('📊 Status update:', {
            status: updatedExecution.status,
            progress: updatedExecution.progress,
            currentStep: updatedExecution.currentStep,
            fullData: updatedExecution
          });

          setExecution(prev => prev ? {
            ...prev,
            status: updatedExecution.status || prev.status,
            progress: updatedExecution.progress !== undefined ? updatedExecution.progress : prev.progress,
            currentStep: updatedExecution.currentStep || prev.currentStep,
            steps: updatedExecution.steps ? updatedExecution.steps.map((updatedStep: any) => ({
              id: updatedStep.id,
              name: updatedStep.name,
              status: updatedStep.status || 'pending',
              outputs: updatedStep.outputs
            })) : prev.steps
          } : null);

          // Handle status changes
          if (updatedExecution.status === 'waiting_review' && currentStep !== 'review') {
            setCurrentStep('review');
            addNotification('⏸️ Workflow paused for human review');
            clearInterval(pollInterval);
          } else if (updatedExecution.status === 'completed' && currentStep !== 'results') {
            setCurrentStep('results');
            addNotification('🎉 Workflow completed successfully!');
            clearInterval(pollInterval);
            if (onComplete) {
              onComplete(executionId);
            }
          } else if (updatedExecution.status === 'failed') {
            setError('Workflow execution failed');
            addNotification('❌ Workflow execution failed');
            clearInterval(pollInterval);
          }
        }
      } catch (error) {
        console.error('❌ Status polling error:', error);
      }
    }, 2000); // Poll every 2 seconds

    // Clean up interval after 5 minutes
    setTimeout(() => {
      clearInterval(pollInterval);
      console.log('📊 Status polling stopped after timeout');
    }, 300000);
  };

  const handleTemplateSelect = (template: WorkflowTemplate) => {
    console.log('🎯 Template selected:', template);
    setSelectedTemplate(template);

    // Initialize inputs based on template
    const initialInputs: Record<string, any> = {};
    if (template.id === 'blog-post-seo') {
      initialInputs.topic = '';
      initialInputs.target_audience = 'startups';
      initialInputs.primary_keyword = '';
    } else if (template.id === 'product-descriptions') {
      initialInputs.brand_voice = '';
      initialInputs.product_category = '';
    }

    console.log('📝 Initial inputs:', initialInputs);
    setWorkflowInputs(initialInputs);

    console.log('🔄 Setting current step to configure');
    setCurrentStep('configure');
    addNotification(`Selected template: ${template.name}`);
  };

  const handleInputChange = (key: string, value: string) => {
    setWorkflowInputs(prev => ({ ...prev, [key]: value }));
  };

  const startWorkflow = async () => {
    if (!selectedTemplate) return;

    try {
      setIsLoading(true);
      setError(null); // Clear any previous errors
      setCurrentStep('executing');
      addNotification('🚀 Starting workflow execution...');

      console.log('🚀 Starting workflow with template:', selectedTemplate.id);
      console.log('📋 Workflow inputs:', workflowInputs);
      console.log('🔄 Current step set to:', 'executing');

      // Create and execute workflow
      const response = await fetch('/api/workflow/create', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          templateId: selectedTemplate.id,
          inputs: workflowInputs,
          userId: 'user-123' // TODO: Get from auth
        })
      });

      console.log('📡 API Response status:', response.status);
      console.log('📡 API Response headers:', Object.fromEntries(response.headers.entries()));

      const result = await response.json();
      console.log('📡 API Response body:', result);

      if (result.success) {
        console.log('✅ Workflow creation successful:', result.data);

        const executionData: WorkflowExecution = {
          id: result.data.executionId,
          status: 'running',
          progress: 0,
          steps: (selectedTemplate.steps || []).map(step => ({
            id: step.id,
            name: step.name,
            status: 'pending'
          }))
        };

        setExecution(executionData);
        addNotification(`✅ Workflow started: ${result.data.executionId ? result.data.executionId.slice(-8) : 'Unknown'}`);

        // Start real-time status monitoring
        startStatusPolling(result.data.executionId);

        // Auto-start agent collaboration for agent-enhanced templates
        if (selectedTemplate.consultationEnabled) {
          console.log('🤖 Agent consultation enabled, starting collaboration monitoring');
          setTimeout(() => {
            setCurrentStep('collaboration');
            setIsCollaborationActive(true);
            addNotification('🤖 Agent collaboration started automatically');
          }, 1000);
        }
      } else {
        console.error('❌ Workflow creation failed:', result);
        const errorMessage = result.error || result.message || 'Failed to start workflow';
        setError(errorMessage);
        addNotification(`❌ ${errorMessage}`);
        setCurrentStep('configure');
      }
    } catch (error) {
      console.error('❌ Failed to start workflow:', error);
      const errorMessage = error instanceof Error ? error.message : 'Failed to start workflow';
      setError(errorMessage);
      addNotification(`❌ ${errorMessage}`);
      setCurrentStep('configure');
    } finally {
      setIsLoading(false);
    }
  };

  const submitReview = async (decision: 'approved' | 'rejected', feedback?: string) => {
    if (!execution?.id) return;

    try {
      const response = await fetch('/api/workflow/execution/review', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          executionId: execution.id,
          stepId: 'human-review',
          decision,
          feedback: feedback || '',
          reviewer: 'user-123' // TODO: Get from auth
        })
      });

      const result = await response.json();
      
      if (result.success) {
        addNotification(`Review ${decision}: Workflow resuming...`);
        setCurrentStep('executing');
      } else {
        setError(result.error || 'Failed to submit review');
      }
    } catch (error) {
      console.error('Failed to submit review:', error);
      setError('Failed to submit review');
    }
  };

  const publishToCMS = async (artifactId: string, artifact: any) => {
    setPublishingStatus(prev => ({ ...prev, [artifactId]: 'publishing' }));

    try {
      const response = await fetch(`/api/cms/publish`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          artifactId,
          title: artifact.title,
          content: artifact.content,
          type: artifact.type,
          executionId: artifact.executionId,
          stepId: artifact.stepId,
          metadata: {
            workflowGenerated: true,
            originalArtifactId: artifactId,
            generatedAt: artifact.createdAt,
            approvedBy: artifact.approvedBy,
            approvedAt: artifact.approvedAt
          }
        })
      });

      const result = await response.json();

      if (result.success) {
        setPublishingStatus(prev => ({ ...prev, [artifactId]: 'published' }));
        setPublishResults(prev => ({
          ...prev,
          [artifactId]: { url: result.data?.url || result.data?.id }
        }));
        addNotification('✅ Published to CMS successfully');
      } else {
        setPublishingStatus(prev => ({ ...prev, [artifactId]: 'failed' }));
        setPublishResults(prev => ({
          ...prev,
          [artifactId]: { error: result.error || 'Failed to publish' }
        }));
        addNotification('❌ Failed to publish to CMS');
      }
    } catch (err) {
      setPublishingStatus(prev => ({ ...prev, [artifactId]: 'failed' }));
      setPublishResults(prev => ({
        ...prev,
        [artifactId]: { error: 'Failed to publish to CMS' }
      }));
      console.error('CMS publish error:', err);
      addNotification('❌ Publishing error occurred');
    }
  };

  const downloadArtifact = (artifact: any) => {
    const content = typeof artifact.content === 'string'
      ? artifact.content
      : JSON.stringify(artifact.content, null, 2);

    const blob = new Blob([content], { type: 'text/plain' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `${artifact.title.replace(/[^a-z0-9]/gi, '_').toLowerCase()}.txt`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  const submitReviewDecision = async (reviewId: string, decision: 'approve' | 'reject', feedback?: string) => {
    try {
      setIsLoading(true);
      const response = await fetch(`/api/review/${reviewId}`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          decision,
          edits: feedback || undefined,
          reviewer: 'current-user',
          metadata: {
            submittedAt: new Date().toISOString(),
            reviewType: reviewData?.type
          }
        })
      });

      const result = await response.json();

      if (result.success) {
        setReviewData((prev: any) => prev ? { ...prev, status: 'completed' } : null);
        addNotification(`✅ Review ${decision}d successfully`);

        // If there's a workflow execution, continue to results
        if (result.data?.workflowExecutionId) {
          await loadResults(result.data.workflowExecutionId);
        }
      } else {
        setError(result.error || 'Failed to submit review');
      }
    } catch (err) {
      setError('Failed to submit review');
      console.error(err);
    } finally {
      setIsLoading(false);
    }
  };

  const resetWorkflow = () => {
    setCurrentStep('template');
    setSelectedTemplate(null);
    setWorkflowInputs({});
    setExecution(null);
    setResults(null);
    setReviewData(null);
    setReviewVersions([]);
    setError(null);
    setIsCollaborationActive(false);
    setCollaborationResult(null);
    setAgentActivities([]);
    setPublishingStatus({});
    setPublishResults({});
    addNotification('Workflow reset');

    // Clear URL parameters
    router.replace('/workflow/unified', { scroll: false });
  };

  // Debug logging for render
  console.log('🎨 Rendering UnifiedWorkflowExperience:', {
    currentStep,
    selectedTemplate: selectedTemplate?.name,
    hasTemplates: templates.length > 0,
    isLoading
  });

  return (
    <ErrorBoundary
      onError={(error, errorInfo) => {
        console.error('Workflow UI Error:', error, errorInfo);
        addNotification('❌ UI Error: Please refresh the page');
      }}
    >
      <div className="min-h-screen bg-gray-50">
      {/* Navigation System */}
      <WorkflowNavigationSystem
        navigationState={navigationState}
        workflowName={selectedTemplate?.name || results?.workflow?.name || 'AI Workflow Studio'}
        showBreadcrumbs={true}
        showStatusBadge={true}
        showActionButtons={true}
      />

      {/* Header with Progress */}
      <div className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center space-x-4">
              {onBack && (
                <button
                  onClick={onBack}
                  className="text-gray-600 hover:text-gray-900"
                >
                  <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
                  </svg>
                </button>
              )}
              <div>
                <h1 className="text-xl font-semibold text-gray-900">AI Workflow Studio</h1>
                <p className="text-sm text-gray-600">Create content with intelligent agent collaboration</p>
              </div>
            </div>

            {/* Progress Indicator */}
            <div className="flex items-center space-x-2">
              {['Template', 'Configure', 'Execute', 'Collaborate', 'Review', 'Results'].map((stepName, index) => {
                const stepKeys: WorkflowStep[] = ['template', 'configure', 'executing', 'collaboration', 'review', 'results'];
                const currentStepIndex = stepKeys.indexOf(currentStep);
                const isActive = index === currentStepIndex;
                const isCompleted = index < currentStepIndex;
                const isAccessible = index <= currentStepIndex + 1;
                
                return (
                  <div key={stepName} className="flex items-center">
                    <div className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium transition-colors ${
                      isActive ? 'bg-blue-600 text-white' :
                      isCompleted ? 'bg-green-600 text-white' :
                      isAccessible ? 'bg-gray-200 text-gray-600' :
                      'bg-gray-100 text-gray-400'
                    }`}>
                      {isCompleted ? '✓' : index + 1}
                    </div>
                    {index < 5 && (
                      <div className={`w-8 h-0.5 mx-1 ${
                        isCompleted ? 'bg-green-600' : 'bg-gray-200'
                      }`} />
                    )}
                  </div>
                );
              })}
            </div>
          </div>
        </div>
      </div>

      {/* Notifications */}
      {notifications.length > 0 && (
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-2">
          <div className="space-y-2">
            {notifications.map((notification, index) => (
              <div
                key={index}
                className="bg-blue-50 border border-blue-200 rounded-lg p-3 text-sm text-blue-800 animate-fade-in"
              >
                🔔 {notification}
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Error Display */}
      {error && (
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-2">
          <div className="bg-red-50 border border-red-200 rounded-lg p-4 text-sm text-red-800">
            ❌ {error}
            <button
              onClick={() => setError(null)}
              className="ml-4 text-red-600 hover:text-red-800 underline"
            >
              Dismiss
            </button>
          </div>
        </div>
      )}

      {/* Main Content */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {currentStep === 'template' && (
          <div className="space-y-6">
            <div className="text-center">
              <h2 className="text-3xl font-bold text-gray-900 mb-4">Choose Your Workflow Template</h2>
              <p className="text-lg text-gray-600 max-w-2xl mx-auto">
                Select a template that matches your content goals. Agent-enhanced templates include intelligent collaboration for better results.
              </p>
            </div>

            {/* Debug Information */}
            {process.env.NODE_ENV === 'development' && (
              <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-4">
                <h3 className="text-sm font-medium text-blue-800 mb-2">Template Debug Info</h3>
                <div className="text-xs text-blue-700 space-y-1">
                  <div>Current Step: {currentStep}</div>
                  <div>Is Loading: {isLoading ? 'Yes' : 'No'}</div>
                  <div>Templates Count: {templates.length}</div>
                  <div>Templates Array: {JSON.stringify(templates.map(t => ({ id: t.id, name: t.name })), null, 2)}</div>
                </div>
              </div>
            )}

            {isLoading ? (
              <div className="flex items-center justify-center py-12">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
                <span className="ml-3 text-gray-600">Loading templates...</span>
              </div>
            ) : templates.length === 0 ? (
              <div className="text-center py-12">
                <div className="text-gray-500 mb-4">No templates available</div>
                <button
                  onClick={loadTemplates}
                  className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
                >
                  Retry Loading Templates
                </button>
              </div>
            ) : (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {templates.map((template) => (
                  <div
                    key={template.id}
                    onClick={() => handleTemplateSelect(template)}
                    className="bg-white rounded-lg border border-gray-200 hover:border-blue-300 hover:shadow-lg transition-all cursor-pointer group"
                  >
                    <div className="p-6">
                      <div className="flex items-start justify-between mb-3">
                        <div>
                          <h3 className="text-lg font-semibold text-gray-900 group-hover:text-blue-900">
                            {template.name}
                          </h3>
                          <div className="flex items-center space-x-2 mt-1">
                            <span className={`px-2 py-1 text-xs font-medium rounded ${
                              template.difficulty === 'easy' ? 'bg-green-100 text-green-800' :
                              template.difficulty === 'medium' ? 'bg-yellow-100 text-yellow-800' :
                              'bg-red-100 text-red-800'
                            }`}>
                              {template.difficulty}
                            </span>
                            <span className="text-xs text-gray-500">~{template.estimatedTime}min</span>
                          </div>
                        </div>

                        {template.consultationEnabled && (
                          <div className="flex items-center space-x-1">
                            <span className="text-sm">🤖</span>
                            <span className="text-xs text-blue-600 font-medium">{template.agentCount}</span>
                          </div>
                        )}
                      </div>

                      <p className="text-gray-600 text-sm mb-4">{template.description}</p>

                      {template.consultationEnabled && (
                        <div className="mb-4">
                          <div className="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                            <span className="mr-1">🤖</span>
                            Agent-Enhanced
                          </div>
                        </div>
                      )}

                      <div className="space-y-2">
                        <div className="text-xs text-gray-600 font-medium">Workflow Steps:</div>
                        <div className="space-y-1">
                          {(template.steps || []).slice(0, 3).map((step, index) => (
                            <div key={step.id} className="flex items-center space-x-2 text-xs">
                              <span className="w-4 h-4 bg-blue-100 text-blue-600 rounded-full flex items-center justify-center font-medium">
                                {index + 1}
                              </span>
                              <span className="text-gray-700">{step.name}</span>
                            </div>
                          ))}
                          {(template.steps || []).length > 3 && (
                            <div className="text-xs text-gray-500 ml-6">
                              +{(template.steps || []).length - 3} more steps
                            </div>
                          )}
                        </div>
                      </div>

                      <div className="mt-4 pt-3 border-t border-gray-100">
                        <div className="flex items-center justify-between text-xs">
                          <span className="text-gray-500">{template.category}</span>
                          <span className="text-blue-600 font-medium">Select Template →</span>
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        )}

        {currentStep === 'configure' && selectedTemplate && (
          <div className="max-w-2xl mx-auto space-y-6">
            {/* Debug Information */}
            {process.env.NODE_ENV === 'development' && (
              <div className="bg-green-50 border border-green-200 rounded-lg p-4 mb-4">
                <h3 className="text-sm font-medium text-green-800 mb-2">Configure Debug Info</h3>
                <div className="text-xs text-green-700 space-y-1">
                  <div>Current Step: {currentStep}</div>
                  <div>Selected Template: {selectedTemplate?.name || 'None'}</div>
                  <div>Template ID: {selectedTemplate?.id || 'None'}</div>
                  <div>Workflow Inputs: {JSON.stringify(workflowInputs, null, 2)}</div>
                </div>
              </div>
            )}

            <div className="text-center">
              <h2 className="text-2xl font-bold text-gray-900 mb-2">{selectedTemplate.name}</h2>
              <p className="text-gray-600">{selectedTemplate.description}</p>
              <button
                onClick={() => setCurrentStep('template')}
                className="mt-2 text-blue-600 hover:text-blue-800 text-sm font-medium"
              >
                ← Change Template
              </button>
            </div>

            <div className="bg-white rounded-lg shadow-lg p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Configure Your Workflow</h3>

              <div className="space-y-4">
                {Object.entries(workflowInputs).map(([key, value]) => (
                  <div key={key}>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      {key.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}
                    </label>
                    {key.includes('description') || key.includes('voice') ? (
                      <textarea
                        value={value}
                        onChange={(e) => handleInputChange(key, e.target.value)}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                        rows={3}
                        placeholder={`Enter ${key.replace(/_/g, ' ')}`}
                      />
                    ) : (
                      <input
                        type="text"
                        value={value}
                        onChange={(e) => handleInputChange(key, e.target.value)}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                        placeholder={`Enter ${key.replace(/_/g, ' ')}`}
                      />
                    )}
                  </div>
                ))}
              </div>

              {selectedTemplate.consultationEnabled && (
                <div className="mt-6 p-4 bg-blue-50 border border-blue-200 rounded-lg">
                  <h4 className="font-medium text-blue-900 mb-2">🤖 Agent Collaboration Enabled</h4>
                  <p className="text-sm text-blue-800 mb-2">
                    This workflow includes intelligent agent consultation with {selectedTemplate.agentCount} specialized agents.
                  </p>
                  <div className="text-xs text-blue-700">
                    <strong>Agents:</strong> SEO Specialist, Market Research, Content Strategy
                  </div>
                </div>
              )}

              <div className="mt-6 flex justify-between">
                <button
                  onClick={() => setCurrentStep('template')}
                  className="px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50"
                >
                  Back
                </button>
                <button
                  onClick={startWorkflow}
                  disabled={isLoading || Object.values(workflowInputs).some(v => !v)}
                  className="px-6 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  {isLoading ? 'Starting...' : 'Start Workflow'}
                </button>
              </div>
            </div>
          </div>
        )}

        {/* Debug Information */}
        {process.env.NODE_ENV === 'development' && (
          <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-4">
            <h3 className="text-sm font-medium text-yellow-800 mb-2">Debug Info</h3>
            <div className="text-xs text-yellow-700 space-y-1">
              <div>Current Step: {currentStep}</div>
              <div>Execution: {execution ? 'Present' : 'Null'}</div>
              <div>Execution ID: {execution?.id || 'N/A'}</div>
              <div>Execution Status: {execution?.status || 'N/A'}</div>
              <div>Is Loading: {isLoading ? 'Yes' : 'No'}</div>
              <div>Error: {error || 'None'}</div>
            </div>
          </div>
        )}

        {(currentStep === 'executing' || currentStep === 'collaboration') && execution && (
          <ExecutionDashboard
            execution={execution}
            currentStep={currentStep}
            onRefresh={refreshExecutionStatus}
            onPause={() => {
              // TODO: Implement pause functionality
              addNotification('⏸️ Pause functionality coming soon');
            }}
            onCancel={() => {
              // TODO: Implement cancel functionality
              if (confirm('Are you sure you want to cancel this workflow?')) {
                resetWorkflow();
                addNotification('❌ Workflow cancelled');
              }
            }}
          />
        )}

        {/* Fallback for executing state without execution object */}
        {currentStep === 'executing' && !execution && (
          <div className="space-y-6">
            <div className="text-center">
              <h2 className="text-2xl font-bold text-gray-900 mb-2">Starting Workflow...</h2>
              <p className="text-gray-600">Initializing workflow execution...</p>
            </div>

            <div className="bg-white rounded-lg shadow-lg p-6">
              <div className="flex items-center justify-center space-x-3">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
                <span className="text-gray-700">Setting up workflow execution...</span>
              </div>

              {isLoading && (
                <div className="mt-4 text-center text-sm text-gray-500">
                  This may take a few moments...
                </div>
              )}

              {error && (
                <div className="mt-4 p-4 bg-red-50 border border-red-200 rounded-lg">
                  <div className="text-red-800 text-sm">{error}</div>
                  <button
                    onClick={() => setCurrentStep('configure')}
                    className="mt-2 text-red-600 hover:text-red-800 text-sm underline"
                  >
                    Go back to configuration
                  </button>
                </div>
              )}
            </div>
          </div>
        )}

        {currentStep === 'review' && (
          <div className="space-y-6">
            {/* Loading State */}
            {isLoading && (
              <div className="bg-white rounded-lg shadow-lg p-6">
                <div className="flex items-center justify-center space-x-3">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
                  <span className="text-gray-700">Loading review data...</span>
                </div>
              </div>
            )}

            {/* Error State */}
            {error && !isLoading && (
              <div className="bg-white rounded-lg shadow-lg p-6">
                <div className="text-center">
                  <h2 className="text-2xl font-bold text-gray-900 mb-4">Review Not Available</h2>
                  <div className="bg-red-50 border border-red-200 rounded-lg p-4 mb-4">
                    <p className="text-red-800">{error}</p>
                  </div>
                  <div className="flex gap-3 justify-center">
                    <button
                      onClick={() => {
                        setError(null);
                        const reviewId = searchParams?.get('reviewId');
                        if (reviewId) {
                          loadReviewData(reviewId);
                        }
                      }}
                      className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
                    >
                      🔄 Retry
                    </button>
                    <button
                      onClick={() => setCurrentStep('template')}
                      className="px-4 py-2 bg-gray-600 text-white rounded hover:bg-gray-700"
                    >
                      ← Back to Templates
                    </button>
                  </div>
                </div>
              </div>
            )}

            {/* Enhanced Review Interface */}
            {reviewData && !isLoading && !error ? (
              <div className="bg-white rounded-lg shadow-lg p-6">
                <div className="mb-6">
                  <h2 className="text-2xl font-bold text-gray-900 mb-2">Content Review</h2>
                  <div className="flex items-center gap-4 text-sm text-gray-600">
                    <span>Type: {reviewData.type}</span>
                    <span>Status: {reviewData.status}</span>
                    {reviewData.deadline && (
                      <span className={new Date() > new Date(reviewData.deadline) ? 'text-red-600' : ''}>
                        Deadline: {new Date(reviewData.deadline).toLocaleString()}
                      </span>
                    )}
                  </div>
                </div>

                {/* Context */}
                {reviewData.content?.context && (
                  <div className="bg-gray-50 p-4 rounded-lg mb-6">
                    <h3 className="font-medium mb-2">Context</h3>
                    {reviewData.content.context.workflowName && (
                      <p className="text-sm text-gray-600">Workflow: {reviewData.content.context.workflowName}</p>
                    )}
                    {reviewData.content.context.stepName && (
                      <p className="text-sm text-gray-600">Step: {reviewData.content.context.stepName}</p>
                    )}
                  </div>
                )}

                {/* Instructions */}
                <div className="bg-blue-50 p-4 rounded-lg mb-6">
                  <h3 className="font-medium mb-2">Instructions</h3>
                  <p className="text-gray-700">{reviewData.instructions}</p>
                </div>

                {/* Content to Review */}
                <div className="mb-6">
                  <div className="flex items-center justify-between mb-3">
                    <h3 className="text-lg font-medium">Content: {reviewData.content?.title}</h3>
                    {reviewVersions.length > 1 && (
                      <button
                        onClick={() => setShowVersionComparison(!showVersionComparison)}
                        className="px-3 py-1 text-sm bg-blue-100 text-blue-700 rounded hover:bg-blue-200 transition-colors"
                      >
                        {showVersionComparison ? 'Hide' : 'Show'} Version History ({reviewVersions.length} versions)
                      </button>
                    )}
                  </div>

                  <div className="border rounded-lg p-4 bg-white">
                    <pre className="whitespace-pre-wrap text-sm">
                      {typeof reviewData.content?.data === 'string'
                        ? reviewData.content.data
                        : JSON.stringify(reviewData.content?.data, null, 2)
                      }
                    </pre>
                  </div>
                </div>

                {/* Version Comparison */}
                {showVersionComparison && reviewVersions.length > 1 && (
                  <div className="mb-6 bg-yellow-50 border border-yellow-200 rounded-lg p-6">
                    <h3 className="text-lg font-semibold text-yellow-900 mb-4">📋 Version History & Comparison</h3>
                    <div className="space-y-4 max-h-96 overflow-y-auto">
                      {reviewVersions.map((version, index) => (
                        <div key={version.version} className={`border rounded-lg p-4 ${
                          index === 0 ? 'bg-green-50 border-green-200' : 'bg-white border-gray-200'
                        }`}>
                          <div className="flex items-center justify-between mb-3">
                            <div className="flex items-center space-x-3">
                              <span className={`px-2 py-1 rounded text-sm font-medium ${
                                index === 0 ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'
                              }`}>
                                {index === 0 ? 'Current Version' : `Version ${version.versionNumber}`}
                              </span>
                              <span className="text-sm text-gray-600">
                                {new Date(version.timestamp).toLocaleString()}
                              </span>
                            </div>
                          </div>
                          {version.userFeedback && (
                            <div className="mb-3 p-3 bg-red-50 border border-red-200 rounded">
                              <h5 className="font-medium text-red-900 mb-1">User Feedback:</h5>
                              <p className="text-sm text-red-800">{version.userFeedback}</p>
                            </div>
                          )}
                          <div className="bg-gray-50 p-3 rounded border max-h-32 overflow-y-auto">
                            <pre className="whitespace-pre-wrap text-xs text-gray-700">
                              {typeof version.content === 'string' ? version.content : JSON.stringify(version.content, null, 2)}
                            </pre>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                )}

                {/* Review Actions */}
                {reviewData.status !== 'completed' && (
                  <div className="flex gap-4">
                    <button
                      onClick={() => submitReviewDecision(reviewData.id, 'approve')}
                      disabled={isLoading}
                      className="px-6 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 disabled:opacity-50"
                    >
                      ✅ Approve
                    </button>
                    <button
                      onClick={() => {
                        const feedback = prompt('Please provide feedback for rejection:');
                        if (feedback) {
                          submitReviewDecision(reviewData.id, 'reject', feedback);
                        }
                      }}
                      disabled={isLoading}
                      className="px-6 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 disabled:opacity-50"
                    >
                      ❌ Reject
                    </button>
                  </div>
                )}
              </div>
            ) : execution && (
              <HumanReviewInterface
                execution={execution}
                onApprove={() => submitReview('approved')}
                onReject={(feedback) => submitReview('rejected', feedback)}
                isSubmitting={isLoading}
              />
            )}
          </div>
        )}

        {currentStep === 'results' && (results || (execution && selectedTemplate)) && (
          <div className="space-y-6">
            {/* Success Banner */}
            <div className="bg-green-50 border border-green-200 rounded-lg p-4">
              <div className="flex items-center">
                <span className="text-green-600 mr-3 text-2xl">🎉</span>
                <div>
                  <h2 className="text-lg font-medium text-green-800">Workflow Completed Successfully!</h2>
                  <p className="text-sm text-green-700 mt-1">
                    Your AI-powered content workflow has finished. Review the results below and publish to your CMS.
                  </p>
                </div>
              </div>
            </div>

            {results ? (
              <div className="space-y-6">
                {/* Workflow Info */}
                {results.workflow && (
                  <div className="bg-gray-50 p-4 rounded-lg">
                    <h3 className="font-medium mb-2">Workflow: {results.workflow.name}</h3>
                    <p className="text-sm text-gray-600">{results.workflow.description}</p>
                  </div>
                )}

                {/* Execution Summary */}
                <div className="bg-white border rounded-lg p-4">
                  <h3 className="font-medium mb-3">Execution Summary</h3>
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                    <div>
                      <span className="text-gray-500">Started:</span>
                      <p>{new Date(results.execution.startedAt).toLocaleString()}</p>
                    </div>
                    {results.execution.completedAt && (
                      <div>
                        <span className="text-gray-500">Completed:</span>
                        <p>{new Date(results.execution.completedAt).toLocaleString()}</p>
                      </div>
                    )}
                    <div>
                      <span className="text-gray-500">Total Steps:</span>
                      <p>{results.steps?.length || 0}</p>
                    </div>
                    <div>
                      <span className="text-gray-500">Artifacts:</span>
                      <p>{results.artifacts?.length || 0}</p>
                    </div>
                  </div>
                </div>

                {/* Publishing Status Summary */}
                {results.artifacts.length > 0 && (
                  <div className="bg-white border rounded-lg p-4">
                    <h3 className="font-medium mb-3">Publishing Status</h3>
                    <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                      <div>
                        <span className="text-gray-500">Ready to Publish:</span>
                        <p className="text-blue-600 font-medium">
                          {results.artifacts.filter(a => a.status === 'approved' && !publishingStatus[a.id]).length}
                        </p>
                      </div>
                      <div>
                        <span className="text-gray-500">Published:</span>
                        <p className="text-green-600 font-medium">
                          {Object.values(publishingStatus).filter(status => status === 'published').length}
                        </p>
                      </div>
                      <div>
                        <span className="text-gray-500">Failed:</span>
                        <p className="text-red-600 font-medium">
                          {Object.values(publishingStatus).filter(status => status === 'failed').length}
                        </p>
                      </div>
                      <div>
                        <span className="text-gray-500">Total Artifacts:</span>
                        <p className="font-medium">{results.artifacts.length}</p>
                      </div>
                    </div>
                  </div>
                )}

                {/* Generated Artifacts */}
                {results.artifacts.length > 0 && (
                  <div className="space-y-4">
                    <h3 className="text-xl font-semibold">📝 Generated Content & Artifacts</h3>
                    {results.artifacts.map((artifact) => (
                      <div key={artifact.id} className="bg-white border rounded-lg p-4">
                        <div className="flex items-center justify-between mb-3">
                          <div className="flex items-center space-x-3">
                            <h4 className="font-medium">{artifact.title}</h4>
                          </div>
                          <div className="flex gap-2 text-xs">
                            <span className="bg-gray-100 px-2 py-1 rounded">{artifact.type}</span>
                            <span className={`px-2 py-1 rounded ${
                              artifact.status === 'approved' ? 'bg-green-100 text-green-700' :
                              artifact.status === 'rejected' ? 'bg-red-100 text-red-700' :
                              artifact.status === 'pending_approval' ? 'bg-yellow-100 text-yellow-700' :
                              'bg-gray-100 text-gray-700'
                            }`}>
                              {artifact.status}
                            </span>
                          </div>
                        </div>

                        <div className="bg-gray-50 p-3 rounded border">
                          <div className="flex items-center justify-between mb-2">
                            <h5 className="font-medium text-gray-900">Content Preview</h5>
                            <span className="text-xs text-gray-600">
                              {typeof artifact.content === 'string' ? artifact.content.length : 'N/A'} characters
                            </span>
                          </div>
                          <div className="max-h-40 overflow-y-auto">
                            <pre className="whitespace-pre-wrap text-sm">
                              {typeof artifact.content === 'string' ? artifact.content : JSON.stringify(artifact.content, null, 2)}
                            </pre>
                          </div>
                        </div>

                        {/* Publishing Actions */}
                        {artifact.status === 'approved' && (
                          <div className="mt-4 p-4 bg-blue-50 border border-blue-200 rounded">
                            <h5 className="font-medium text-blue-800 mb-3">Ready for Publishing</h5>
                            <div className="flex gap-2">
                              <button
                                onClick={() => publishToCMS(artifact.id, artifact)}
                                disabled={publishingStatus[artifact.id] === 'publishing'}
                                className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 disabled:opacity-50"
                              >
                                {publishingStatus[artifact.id] === 'publishing' ? 'Publishing...' : '📤 Publish to CMS'}
                              </button>
                              <button
                                onClick={() => downloadArtifact(artifact)}
                                className="px-4 py-2 bg-gray-600 text-white rounded hover:bg-gray-700"
                              >
                                📥 Download
                              </button>
                            </div>

                            {publishResults[artifact.id]?.url && (
                              <div className="mt-3 p-2 bg-green-50 border border-green-200 rounded">
                                <span className="text-sm text-green-700">Published at: </span>
                                <a
                                  href={publishResults[artifact.id].url}
                                  target="_blank"
                                  rel="noopener noreferrer"
                                  className="text-sm text-blue-600 hover:underline"
                                >
                                  {publishResults[artifact.id].url}
                                </a>
                              </div>
                            )}

                            {publishResults[artifact.id]?.error && (
                              <div className="mt-3 p-2 bg-red-50 border border-red-200 rounded">
                                <span className="text-sm text-red-700">Error: {publishResults[artifact.id].error}</span>
                              </div>
                            )}
                          </div>
                        )}
                      </div>
                    ))}
                  </div>
                )}

                {/* Action Buttons */}
                <div className="flex gap-3">
                  <button
                    onClick={resetWorkflow}
                    className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
                  >
                    🚀 Start New Workflow
                  </button>
                  <button
                    onClick={() => window.location.reload()}
                    className="px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700"
                  >
                    🔄 Refresh
                  </button>
                  {onBack && (
                    <button
                      onClick={onBack}
                      className="px-4 py-2 bg-gray-600 text-white rounded hover:bg-gray-700"
                    >
                      ← Back to Dashboard
                    </button>
                  )}
                </div>
              </div>
            ) : execution && selectedTemplate && (
              <ResultsDashboard
                execution={execution}
                selectedTemplate={selectedTemplate}
                onReset={resetWorkflow}
                onBack={onBack}
              />
            )}
          </div>
        )}
      </div>
    </div>
    </ErrorBoundary>
  );
}
